// Provider type definitions

export interface Provider {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  image: string;
  rating: number;
  jobs?: number;
  reviews?: number;
  verified: boolean;
  price?: number;
  bio?: string;
  experience?: number;
  services?: string[] | { id: string; name: string; price: number }[];
  workingAreas?: string[];
  availability?: Availability[];
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface Availability {
  id: string;
  providerId: string;
  date: string;
  startTime: string;
  endTime: string;
  isBooked: boolean;
}

export interface ProviderWithServices extends Provider {
  services: {
    id: string;
    name: string;
    price: number;
  }[];
}
