/**
 * Development utilities for testing onboarding flow
 * These functions should only be used during development and testing
 */

import * as authStorage from './authStorage';

/**
 * Reset the app to first-launch state
 * This will clear all data and force the onboarding flow to show
 */
export const resetToFirstLaunch = async (): Promise<void> => {
  try {
    console.log('🔄 Resetting app to first-launch state...');
    
    // Clear all app data
    await authStorage.clearAllAppData();
    
    console.log('✅ App reset complete. Restart the app to see onboarding flow.');
  } catch (error) {
    console.error('❌ Error resetting app:', error);
  }
};

/**
 * Simulate completing onboarding without going through the flow
 */
export const simulateOnboardingComplete = async (): Promise<void> => {
  try {
    console.log('🔄 Simulating onboarding completion...');
    
    // Mark onboarding as completed
    await authStorage.saveOnboardingStatus(true);
    
    console.log('✅ Onboarding marked as complete. Restart the app to skip onboarding.');
  } catch (error) {
    console.error('❌ Error simulating onboarding completion:', error);
  }
};

/**
 * Check current app state for debugging
 */
export const checkAppState = async (): Promise<void> => {
  try {
    console.log('🔍 Checking current app state...');
    
    const onboardingCompleted = await authStorage.getOnboardingStatus();
    const isAuthenticated = await authStorage.getAuthState();
    const userRole = await authStorage.getUserRole();
    const isFresh = await authStorage.isFreshInstall();
    
    console.log('📊 App State:');
    console.log(`  - Onboarding completed: ${onboardingCompleted}`);
    console.log(`  - Is authenticated: ${isAuthenticated}`);
    console.log(`  - User role: ${userRole || 'None'}`);
    console.log(`  - Is fresh install: ${isFresh}`);
  } catch (error) {
    console.error('❌ Error checking app state:', error);
  }
};

/**
 * Test the onboarding flow by cycling through states
 */
export const testOnboardingFlow = async (): Promise<void> => {
  try {
    console.log('🧪 Testing onboarding flow...');
    
    // Step 1: Check initial state
    console.log('\n1️⃣ Initial state:');
    await checkAppState();
    
    // Step 2: Reset to first launch
    console.log('\n2️⃣ Resetting to first launch...');
    await resetToFirstLaunch();
    await checkAppState();
    
    // Step 3: Simulate onboarding completion
    console.log('\n3️⃣ Simulating onboarding completion...');
    await simulateOnboardingComplete();
    await checkAppState();
    
    console.log('\n✅ Onboarding flow test complete!');
    console.log('💡 Restart the app to see the changes in action.');
  } catch (error) {
    console.error('❌ Error testing onboarding flow:', error);
  }
};

// Export all functions for easy access in development
export const devUtils = {
  resetToFirstLaunch,
  simulateOnboardingComplete,
  checkAppState,
  testOnboardingFlow,
};

// Make it available globally in development
if (__DEV__) {
  // Temporarily disabled to fix Hermes "property is not configurable" error
  // (global as any).devUtils = devUtils;
  console.log('🛠️ Development utilities loaded. Import devUtils to access them.');
}
