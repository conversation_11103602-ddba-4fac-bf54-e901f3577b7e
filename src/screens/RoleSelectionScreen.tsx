import React, { useCallback, useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Image,
  Dimensions,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import type { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Feather } from "@expo/vector-icons";
import { useTheme } from "../context/ThemeContext";
import { useAuth } from "../context/AuthContext";
import type { RootStackParamList } from "../navigation/RootNavigator";
import * as authStorage from "../utils/authStorage";
import { UserRole } from "../types/user";
import { STORAGE_KEYS } from "../config/constants";

type RoleSelectionScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

/**
 * RoleSelectionScreen Component
 *
 * Allows users to select their role (Customer or Cleaner) after completing onboarding
 */
const RoleSelectionScreen = () => {
  const navigation = useNavigation<RoleSelectionScreenNavigationProp>();
  const theme = useTheme();
  const { setUserRole, userRole, setIsAuthenticated } = useAuth();
  const [isCheckingRole, setIsCheckingRole] = useState(true);

  // Get screen dimensions for responsive layout
  const { width } = Dimensions.get("window");

  // Check for saved role on mount (only for returning users, not after onboarding)
  useEffect(() => {
    const checkSavedRole = async () => {
      try {
        // Check if user is authenticated first
        const isAuthenticated = await authStorage.getAuthState();

        if (isAuthenticated) {
          // Get saved role from secure storage for authenticated users
          const savedRole = await authStorage.getUserRole();

          if (savedRole) {
            console.log('Found saved role for authenticated user:', savedRole);

            // Auto-navigate to the appropriate screen based on saved role
            if (savedRole === UserRole.CUSTOMER) {
              navigation.navigate("PhoneVerification");
            } else if (savedRole === UserRole.PROVIDER) {
              navigation.navigate("ProviderLogin");
            }
          }
        }
        // For non-authenticated users (coming from onboarding), let them choose their role
      } catch (error) {
        console.error('Error checking saved role:', error);
      } finally {
        setIsCheckingRole(false);
      }
    };

    checkSavedRole();
  }, [navigation]);

  // Handle role selection
  const handleRoleSelection = useCallback(async (role: "customer" | "provider") => {
    try {
      // Convert to UserRole enum
      const userRoleEnum = role === "customer" ? UserRole.CUSTOMER : UserRole.PROVIDER;

      // Store the user role in the AuthContext
      await setUserRole(userRoleEnum);

      // Store the role in secure storage for persistence
      await authStorage.saveUserRole(userRoleEnum);

      console.log(`Role selected: ${role}, Enum value: ${userRoleEnum}`);

      if (role === "customer") {
        // For customers, navigate to the phone verification screen
        navigation.navigate("PhoneVerification");
      } else {
        // For providers, navigate to the provider login screen
        navigation.navigate("ProviderLogin");
      }
    } catch (error) {
      console.error("Error storing user role:", error);
    }
  }, [navigation, setUserRole]);

  // Handle back button press
  const handleBack = useCallback(() => {
    navigation.goBack();
  }, [navigation]);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar backgroundColor={theme.colors.background} barStyle="dark-content" />

      {/* Back Button */}
      <TouchableOpacity style={styles.backButton} onPress={handleBack}>
        <Feather name="arrow-left" size={24} color={theme.colors.text} />
      </TouchableOpacity>

      <View style={styles.content}>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          How will you use CleanConnect?
        </Text>

        <Text style={[styles.subtitle, { color: theme.colors.textLight }]}>
          Select your role to get started
        </Text>

        {/* Role Options */}
        <View style={styles.optionsContainer}>
          {/* Customer Option */}
          <TouchableOpacity
            style={[
              styles.roleOption,
              { backgroundColor: theme.colors.card, borderColor: theme.colors.border, width: width * 0.85 }
            ]}
            onPress={() => handleRoleSelection("customer")}
          >
            <View style={[styles.iconContainer, { backgroundColor: `${theme.colors.primary}15` }]}>
              <Feather name="user" size={32} color={theme.colors.primary} />
            </View>

            <View style={styles.roleTextContainer}>
              <Text style={[styles.roleTitle, { color: theme.colors.text }]}>
                I'm a Customer
              </Text>

              <Text style={[styles.roleDescription, { color: theme.colors.textLight }]}>
                Book professional cleaning services for your home or office
              </Text>
            </View>

            <Feather name="chevron-right" size={24} color={theme.colors.textLight} />
          </TouchableOpacity>

          {/* Provider Option */}
          <TouchableOpacity
            style={[
              styles.roleOption,
              { backgroundColor: theme.colors.card, borderColor: theme.colors.border, width: width * 0.85 }
            ]}
            onPress={() => handleRoleSelection("provider")}
          >
            <View style={[styles.iconContainer, { backgroundColor: `${theme.colors.secondary}15` }]}>
              <Feather name="briefcase" size={32} color={theme.colors.secondary} />
            </View>

            <View style={styles.roleTextContainer}>
              <Text style={[styles.roleTitle, { color: theme.colors.text }]}>
                I'm a Cleaner
              </Text>

              <Text style={[styles.roleDescription, { color: theme.colors.textLight }]}>
                Offer cleaning services & earn money
              </Text>
            </View>

            <Feather name="chevron-right" size={24} color={theme.colors.textLight} />
          </TouchableOpacity>
        </View>


      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    padding: 16,
    position: 'absolute',
    top: 10,
    left: 10,
    zIndex: 10,
  },
  content: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: "700",
    marginBottom: 12,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 40,
    textAlign: "center",
  },
  optionsContainer: {
    width: "100%",
    alignItems: "center",
    marginBottom: 40,
  },
  roleOption: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  roleTextContainer: {
    flex: 1,
  },
  roleTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 4,
  },
  roleDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  note: {
    fontSize: 14,
    textAlign: "center",
    marginTop: 20,
  },
});

export default RoleSelectionScreen;
