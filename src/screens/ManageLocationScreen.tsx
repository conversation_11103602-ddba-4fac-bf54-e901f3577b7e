import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import Button from '../components/common/Button';
import { RootStackParamList } from '../navigation/RootNavigator';
import Storage from '../utils/storage';
import { Location } from '../context/LocationContext';

type ManageLocationScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;
type ManageLocationScreenRouteProp = RouteProp<RootStackParamList, 'ManageLocation'>;

type LocationType = 'HOME' | 'WORK' | 'OFFICE' | 'OTHER';

interface LocationTypeOption {
  value: LocationType;
  label: string;
  icon: string;
}

const locationTypes: LocationTypeOption[] = [
  { value: 'HOME', label: 'Home', icon: 'home' },
  { value: 'WORK', label: 'Work', icon: 'briefcase' },
  { value: 'OFFICE', label: 'Office', icon: 'building' },
  { value: 'OTHER', label: 'Other', icon: 'map-pin' },
];

/**
 * ManageLocationScreen Component
 * 
 * A screen for adding or editing a location
 */
const ManageLocationScreen = () => {
  const navigation = useNavigation<ManageLocationScreenNavigationProp>();
  const route = useRoute<ManageLocationScreenRouteProp>();
  const theme = useTheme();
  
  const editingLocation = route.params?.location;
  const isEditing = !!editingLocation;

  // State for form fields
  const [locationType, setLocationType] = useState<LocationType>(editingLocation?.type || 'HOME');
  const [locationName, setLocationName] = useState(editingLocation?.name || '');
  const [locationAddress, setLocationAddress] = useState(editingLocation?.address || '');
  const [isDefault, setIsDefault] = useState(editingLocation?.isDefault || false);
  const [isLoading, setIsLoading] = useState(false);

  // Validate form
  const isFormValid = locationName.trim() !== '' && locationAddress.trim() !== '';

  // Handle save location
  const handleSaveLocation = useCallback(async () => {
    if (!isFormValid) return;
    
    setIsLoading(true);
    
    try {
      // Get existing locations
      const savedLocationsJson = await Storage.getItem('savedLocations');
      const savedLocations: Location[] = savedLocationsJson ? JSON.parse(savedLocationsJson) : [];
      
      // Create new location object
      const newLocation: Location = {
        id: editingLocation?.id || `location_${Date.now()}`,
        type: locationType,
        name: locationName.trim(),
        address: locationAddress.trim(),
        isDefault,
      };
      
      let updatedLocations: Location[];
      
      if (isEditing) {
        // Update existing location
        updatedLocations = savedLocations.map(loc => 
          loc.id === newLocation.id ? newLocation : loc
        );
      } else {
        // Add new location
        updatedLocations = [...savedLocations, newLocation];
      }
      
      // If this location is set as default, remove default from others
      if (isDefault) {
        updatedLocations = updatedLocations.map(loc => ({
          ...loc,
          isDefault: loc.id === newLocation.id,
        }));
      }
      
      // Save updated locations
      await Storage.setItem('savedLocations', JSON.stringify(updatedLocations));
      
      // If no default is set and this is the first location, make it default
      if (updatedLocations.length === 1) {
        updatedLocations[0].isDefault = true;
        await Storage.setItem('savedLocations', JSON.stringify(updatedLocations));
      }
      
      // Set current location if this is default or there's only one location
      if (isDefault || updatedLocations.length === 1) {
        await Storage.setItem('currentLocation', JSON.stringify(newLocation));
      }
      
      // Navigate back
      navigation.goBack();
    } catch (error) {
      console.error('Error saving location:', error);
      Alert.alert('Error', 'Failed to save location. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [
    isFormValid, 
    locationType, 
    locationName, 
    locationAddress, 
    isDefault, 
    editingLocation, 
    isEditing, 
    navigation
  ]);

  // Handle delete location
  const handleDeleteLocation = useCallback(() => {
    Alert.alert(
      'Delete Location',
      'Are you sure you want to delete this location?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            setIsLoading(true);
            
            try {
              // Get existing locations
              const savedLocationsJson = await Storage.getItem('savedLocations');
              const savedLocations: Location[] = savedLocationsJson ? JSON.parse(savedLocationsJson) : [];
              
              // Filter out the location to delete
              const updatedLocations = savedLocations.filter(loc => loc.id !== editingLocation?.id);
              
              // Save updated locations
              await Storage.setItem('savedLocations', JSON.stringify(updatedLocations));
              
              // If the deleted location was the current location, update current location
              const currentLocationJson = await Storage.getItem('currentLocation');
              if (currentLocationJson) {
                const currentLocation: Location = JSON.parse(currentLocationJson);
                if (currentLocation.id === editingLocation?.id) {
                  // Set a new current location if available, otherwise clear it
                  if (updatedLocations.length > 0) {
                    const defaultLocation = updatedLocations.find(loc => loc.isDefault) || updatedLocations[0];
                    await Storage.setItem('currentLocation', JSON.stringify(defaultLocation));
                  } else {
                    await Storage.removeItem('currentLocation');
                  }
                }
              }
              
              // Navigate back
              navigation.goBack();
            } catch (error) {
              console.error('Error deleting location:', error);
              Alert.alert('Error', 'Failed to delete location. Please try again.');
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  }, [editingLocation, navigation]);

  // Memoized styles
  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      padding: theme.spacing.lg,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      marginBottom: theme.spacing.md,
      color: theme.colors.text,
    },
    inputContainer: {
      marginBottom: theme.spacing.lg,
    },
    label: {
      fontSize: 16,
      marginBottom: theme.spacing.xs,
      color: theme.colors.text,
    },
    input: {
      backgroundColor: theme.colors.card,
      borderColor: theme.colors.border,
      borderWidth: 1,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      fontSize: 16,
      color: theme.colors.text,
    },
    locationTypesContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginBottom: theme.spacing.lg,
    },
    locationTypeOption: {
      width: '25%',
      padding: theme.spacing.xs,
    },
    locationTypeContent: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: theme.spacing.md,
      borderRadius: theme.borderRadius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      backgroundColor: theme.colors.card,
    },
    locationTypeSelected: {
      borderColor: theme.colors.primary,
      backgroundColor: `${theme.colors.primary}10`,
    },
    locationTypeIcon: {
      marginBottom: theme.spacing.xs,
    },
    locationTypeLabel: {
      fontSize: 14,
      color: theme.colors.text,
    },
    defaultContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.xl,
    },
    checkboxContainer: {
      width: 24,
      height: 24,
      borderRadius: 4,
      borderWidth: 2,
      borderColor: theme.colors.border,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: theme.spacing.md,
    },
    checkboxSelected: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    defaultText: {
      fontSize: 16,
      color: theme.colors.text,
    },
    buttonsContainer: {
      marginTop: theme.spacing.lg,
    },
    deleteButton: {
      marginTop: theme.spacing.md,
    },
  }), [theme]);

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Location Type</Text>
        <View style={styles.locationTypesContainer}>
          {locationTypes.map((type) => (
            <TouchableOpacity
              key={type.value}
              style={styles.locationTypeOption}
              onPress={() => setLocationType(type.value)}
            >
              <View style={[
                styles.locationTypeContent,
                locationType === type.value && styles.locationTypeSelected,
              ]}>
                <Feather
                  name={type.icon as any}
                  size={24}
                  color={locationType === type.value ? theme.colors.primary : theme.colors.textLight}
                  style={styles.locationTypeIcon}
                />
                <Text style={styles.locationTypeLabel}>{type.label}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Location Name</Text>
          <TextInput
            style={styles.input}
            value={locationName}
            onChangeText={setLocationName}
            placeholder="e.g. My Home, Office"
            placeholderTextColor={theme.colors.textLight}
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Address</Text>
          <TextInput
            style={[styles.input, { height: 100, textAlignVertical: 'top' }]}
            value={locationAddress}
            onChangeText={setLocationAddress}
            placeholder="Enter full address"
            placeholderTextColor={theme.colors.textLight}
            multiline
          />
        </View>

        <TouchableOpacity
          style={styles.defaultContainer}
          onPress={() => setIsDefault(!isDefault)}
        >
          <View style={[
            styles.checkboxContainer,
            isDefault && styles.checkboxSelected,
          ]}>
            {isDefault && <Feather name="check" size={16} color="white" />}
          </View>
          <Text style={styles.defaultText}>Set as default location</Text>
        </TouchableOpacity>

        <View style={styles.buttonsContainer}>
          <Button
            title={isEditing ? "Update Location" : "Save Location"}
            variant="primary"
            fullWidth
            onPress={handleSaveLocation}
            disabled={!isFormValid}
            loading={isLoading}
          />
          
          {isEditing && (
            <Button
              title="Delete Location"
              variant="danger"
              fullWidth
              onPress={handleDeleteLocation}
              style={styles.deleteButton}
            />
          )}
        </View>
      </View>
    </ScrollView>
  );
};

export default ManageLocationScreen;
