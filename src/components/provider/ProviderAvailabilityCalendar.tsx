import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator, ScrollView, RefreshControl } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';
import { useProviderAvailability, AvailabilityData } from '../../context/ProviderAvailabilityContext';

interface ProviderAvailabilityCalendarProps {
  onEdit?: (availability: AvailabilityData) => void;
}

const ProviderAvailabilityCalendar: React.FC<ProviderAvailabilityCalendarProps> = ({ onEdit }) => {
  const theme = useTheme();
  const { 
    availabilityData, 
    isLoading, 
    isRefreshing, 
    isOffline,
    handleRefresh 
  } = useProviderAvailability();
  
  // Sort days of week in correct order
  const daysOrder = ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY'];
  const sortedAvailability = [...availabilityData].sort(
    (a, b) => daysOrder.indexOf(a.dayOfWeek) - daysOrder.indexOf(b.dayOfWeek)
  );

  // Format day of week for display
  const formatDayOfWeek = (day: string): string => {
    return day.charAt(0) + day.slice(1).toLowerCase();
  };

  // Format time for display
  const formatTime = (time: string): string => {
    // Convert 24-hour format to 12-hour format
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
  };

  // Styles
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    offlineBar: {
      backgroundColor: theme.colors.notification,
      padding: theme.spacing.sm,
      alignItems: 'center',
    },
    offlineText: {
      color: theme.colors.background,
      fontSize: theme.fontSizes.sm,
      fontWeight: '500',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing.lg,
    },
    loadingText: {
      marginTop: theme.spacing.md,
      fontSize: theme.fontSizes.md,
      color: theme.colors.text,
    },
    calendarContainer: {
      padding: theme.spacing.md,
    },
    dayCard: {
      backgroundColor: theme.colors.card,
      borderRadius: 12,
      marginBottom: theme.spacing.md,
      overflow: 'hidden',
      elevation: 2,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
    },
    dayHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    dayName: {
      fontSize: theme.fontSizes.md,
      fontWeight: '600',
      color: theme.colors.text,
    },
    availabilityStatus: {
      fontSize: theme.fontSizes.sm,
      fontWeight: '500',
    },
    availableStatus: {
      color: theme.colors.success,
    },
    unavailableStatus: {
      color: theme.colors.notification,
    },
    timeContainer: {
      padding: theme.spacing.md,
    },
    timeRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.sm,
    },
    timeLabel: {
      width: 80,
      fontSize: theme.fontSizes.sm,
      color: theme.colors.textLight,
    },
    timeValue: {
      fontSize: theme.fontSizes.md,
      color: theme.colors.text,
      flex: 1,
    },
    editButton: {
      padding: theme.spacing.sm,
    },
    noDataContainer: {
      padding: theme.spacing.lg,
      alignItems: 'center',
      justifyContent: 'center',
    },
    noDataText: {
      fontSize: theme.fontSizes.md,
      color: theme.colors.textLight,
      textAlign: 'center',
      marginTop: theme.spacing.md,
    },
    noDataIcon: {
      marginBottom: theme.spacing.md,
    },
  });

  // Show loading state
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={styles.loadingText}>Loading availability...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {isOffline && (
        <View style={styles.offlineBar}>
          <Text style={styles.offlineText}>You are offline. Using cached data.</Text>
        </View>
      )}

      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        <View style={styles.calendarContainer}>
          {sortedAvailability.length === 0 ? (
            <View style={styles.noDataContainer}>
              <Feather name="calendar" size={60} color={theme.colors.textLight} style={styles.noDataIcon} />
              <Text style={styles.noDataText}>
                No availability data found. Please add your availability schedule.
              </Text>
            </View>
          ) : (
            sortedAvailability.map((day) => (
              <View key={day.id} style={styles.dayCard}>
                <View style={styles.dayHeader}>
                  <Text style={styles.dayName}>{formatDayOfWeek(day.dayOfWeek)}</Text>
                  <Text
                    style={[
                      styles.availabilityStatus,
                      day.isAvailable ? styles.availableStatus : styles.unavailableStatus,
                    ]}
                  >
                    {day.isAvailable ? 'Available' : 'Unavailable'}
                  </Text>
                </View>
                <View style={styles.timeContainer}>
                  <View style={styles.timeRow}>
                    <Text style={styles.timeLabel}>Start Time:</Text>
                    <Text style={styles.timeValue}>{formatTime(day.startTime)}</Text>
                    {onEdit && (
                      <TouchableOpacity
                        style={styles.editButton}
                        onPress={() => onEdit(day)}
                        accessibilityLabel={`Edit ${formatDayOfWeek(day.dayOfWeek)} availability`}
                      >
                        <Feather name="edit-2" size={18} color={theme.colors.accent} />
                      </TouchableOpacity>
                    )}
                  </View>
                  <View style={styles.timeRow}>
                    <Text style={styles.timeLabel}>End Time:</Text>
                    <Text style={styles.timeValue}>{formatTime(day.endTime)}</Text>
                  </View>
                </View>
              </View>
            ))
          )}
        </View>
      </ScrollView>
    </View>
  );
};

export default ProviderAvailabilityCalendar;
