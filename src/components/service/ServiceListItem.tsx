"use client"

import type React from "react"
import { View, Text, StyleSheet, Image, TouchableOpacity } from "react-native"
import { Feather } from "@expo/vector-icons"
import { useTheme } from "../../context/ThemeContext"
import Card from "../common/Card"
import Button from "../common/Button"

interface ServiceListItemProps {
  id: string
  title: string
  description: string
  price: number
  duration: string
  rating: number
  reviews: number
  image: string
  onPress: () => void
}

const ServiceListItem: React.FC<ServiceListItemProps> = ({
  title,
  description,
  price,
  duration,
  rating,
  reviews,
  image,
  onPress,
}) => {
  const theme = useTheme()

  const styles = StyleSheet.create({
    container: {
      marginBottom: theme.spacing.screenPadding,
    },
    content: {
      flexDirection: "row",
    },
    image: {
      width: 120,
      height: 120,
      borderTopLeftRadius: theme.borderRadius.md,
      borderBottomLeftRadius: theme.borderRadius.md,
    },
    details: {
      flex: 1,
      padding: theme.spacing.screenPadding,
    },
    title: {
      fontSize: theme.fonts.sizes.md,
      fontWeight: "600",
      color: theme.colors.text,
      marginBottom: 4,
    },
    description: {
      fontSize: theme.fonts.sizes.xs,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.screenPadding / 2,
    },
    ratingContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: theme.spacing.screenPadding / 2,
    },
    star: {
      color: "#FFD700",
      marginRight: 2,
    },
    ratingText: {
      fontSize: theme.fonts.sizes.xs,
      marginRight: 2,
    },
    reviewsText: {
      fontSize: theme.fonts.sizes.xs,
      color: theme.colors.textSecondary,
      marginRight: theme.spacing.screenPadding / 2,
    },
    separator: {
      height: 12,
      width: 1,
      backgroundColor: theme.colors.border,
      marginHorizontal: theme.spacing.screenPadding / 4,
    },
    durationText: {
      fontSize: theme.fonts.sizes.xs,
      color: theme.colors.textSecondary,
    },
    footer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    price: {
      fontSize: theme.fonts.sizes.md,
      fontWeight: "700",
      color: theme.colors.text,
    },
  })

  return (
    <Card style={styles.container} variant="outlined">
      <TouchableOpacity onPress={onPress} activeOpacity={0.9}>
        <View style={styles.content}>
          <Image source={{ uri: image }} style={styles.image} resizeMode="cover" />
          <View style={styles.details}>
            <Text style={styles.title} numberOfLines={1}>
              {title}
            </Text>
            <Text style={styles.description} numberOfLines={2}>
              {description}
            </Text>
            <View style={styles.ratingContainer}>
              <Feather name="star" size={12} style={styles.star} />
              <Text style={styles.ratingText}>{rating}</Text>
              <Text style={styles.reviewsText}>({reviews})</Text>
              <View style={styles.separator} />
              <Text style={styles.durationText}>Est. {duration}</Text>
            </View>
            <View style={styles.footer}>
              <Text style={styles.price}>D{price}/hr</Text>
              <Button title="Book" onPress={onPress} variant="warning" size="small" />
            </View>
          </View>
        </View>
      </TouchableOpacity>
    </Card>
  )
}

export default ServiceListItem
