import type React from "react"
import { useState } from "react"
import { View, StyleSheet, StatusBar } from "react-native"
import { useTheme } from "../../context/ThemeContext"
import MobileNavigation from "../navigation/MobileNavigation"
import { useSafeAreaInsets } from 'react-native-safe-area-context'

interface AppLayoutProps {
  children: React.ReactNode
  hideNavigation?: boolean
}

const AppLayout: React.FC<AppLayoutProps> = ({ children, hideNavigation = false }) => {
  const theme = useTheme()
  const [activeTab, setActiveTab] = useState("home")
  const insets = useSafeAreaInsets()

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
    },
    navigationContainer: {
      backgroundColor: 'white',
      paddingBottom: insets.bottom,
    },
    statusBarSpace: {
      height: insets.top,
      backgroundColor: theme.colors.primary,
    }
  })

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={theme.colors.primary} barStyle="light-content" />

      {/* Add status bar space for proper mobile display */}
      <View style={styles.statusBarSpace} />

      <View style={styles.content}>
        {children}
      </View>

      {!hideNavigation && (
        <View style={styles.navigationContainer}>
          <MobileNavigation activeTab={activeTab} onTabChange={setActiveTab} />
        </View>
      )}
    </View>
  )
}

export default AppLayout
