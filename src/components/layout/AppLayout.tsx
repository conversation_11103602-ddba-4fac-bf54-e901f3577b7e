"use client"

import type React from "react"
import { useState } from "react"
import { View, StyleSheet, SafeAreaView, StatusBar, Platform } from "react-native"
import { useRoute } from "@react-navigation/native"
import { useResponsive } from "../../hooks/useResponsive"
import { useTheme } from "../../context/ThemeContext"
import MobileNavigation from "../navigation/MobileNavigation"
import { useSafeAreaInsets } from 'react-native-safe-area-context'

interface AppLayoutProps {
  children: React.ReactNode
  hideNavigation?: boolean
}

const AppLayout: React.FC<AppLayoutProps> = ({ children, hideNavigation = false }) => {
  const route = useRoute()
  const { isWeb, deviceType } = useResponsive()
  const theme = useTheme()
  const [activeTab, setActiveTab] = useState("home")
  const insets = useSafeAreaInsets()

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      // Remove paddingTop to avoid excessive space
    },
    content: {
      flex: 1,
    },
    webContent: {
      paddingHorizontal: deviceType === "desktop" ? 24 : 16,
      paddingVertical: 16,
    },
    navigationContainer: {
      backgroundColor: 'white',
      paddingBottom: insets.bottom,
    },
    statusBarSpace: {
      height: insets.top,
      backgroundColor: theme.colors.primary,
    }
  })

  // Get current route name
  const currentRouteName = route.name

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={theme.colors.primary} barStyle="light-content" />

      {/* Add status bar space only when needed */}
      {!isWeb && <View style={styles.statusBarSpace} />}

      {!hideNavigation && isWeb && <WebNavigation currentRoute={currentRouteName} />}

      <View style={[styles.content, isWeb && styles.webContent]}>
        {children}
      </View>

      {!hideNavigation && !isWeb && (
        <View style={styles.navigationContainer}>
          <MobileNavigation activeTab={activeTab} onTabChange={setActiveTab} />
        </View>
      )}
    </View>
  )
}

export default AppLayout
