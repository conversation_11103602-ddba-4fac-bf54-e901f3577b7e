import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useAuth } from '../context/AuthContext';
import { UserRole } from '../types/user';
import AuthNavigator from './AuthNavigator';
import CustomerNavigator from './CustomerNavigator';
import ProviderNavigator from './ProviderNavigator';
import LoadingScreen from '../screens/LoadingScreen';

// App stack param list
export type AppStackParamList = {
  Auth: undefined;
  Customer: undefined;
  Provider: undefined;
  Loading: undefined;
};

// Create stack navigator
const Stack = createNativeStackNavigator<AppStackParamList>();

// App navigator component (without NavigationContainer - should be wrapped externally)
const AppNavigator: React.FC = () => {
  const { isLoading, isAuthenticated, user } = useAuth();

  // Show loading screen while checking authentication
  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      {!isAuthenticated ? (
        // Auth routes
        <Stack.Screen name="Auth" component={AuthNavigator} />
      ) : user?.role === UserRole.PROVIDER ? (
        // Provider routes
        <Stack.Screen name="Provider" component={ProviderNavigator} />
      ) : (
        // Customer routes
        <Stack.Screen name="Customer" component={CustomerNavigator} />
      )}
    </Stack.Navigator>
  );
};

export default AppNavigator;
